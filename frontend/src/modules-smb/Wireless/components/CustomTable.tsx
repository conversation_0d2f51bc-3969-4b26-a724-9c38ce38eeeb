import React, { forwardRef, useEffect, useImperative<PERSON>andle, useMemo, useState, useCallback } from "react";
import { Flex, Table, Input } from "antd";
import { useDispatch } from "react-redux";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import { useTableInitialElement } from "@/modules-ampcon/hooks/useModalTable";
import { updateAlarmSearch, updateAlarmSearchStatus } from "@/store/modules/common/alarm_slice";
import { handleTableChange } from "@/modules-ampcon/components/custom_table";
import ColumnSelector from '@/modules-smb/Wireless/components/Modals/ColumnSelectorWithOrderProps';
import ResizableTable from '@/modules-smb/Wireless/components/Tables/ResizableTable';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { useGetPreferences } from '@/modules-smb/hooks/Network/Account';
import styles from './CustomTable.module.css';

dayjs.extend(utc);
dayjs.extend(timezone);

// ==================== 工具函数 ====================

/**
 * 创建匹配模式映射
 * @param fields 字段配置数组
 * @returns 匹配模式映射对象
 */
export const createMatchMode = (fields: any[]) => {
    const matchModes: Record<string, string> = {};
    fields.forEach(field => {
        matchModes[field.name] = field.matchMode;
    });
    return matchModes;
};

/**
 * 创建过滤字段配置
 * @param filters 过滤器对象
 * @param matchModes 匹配模式映射
 * @returns 过滤字段配置数组
 */
const createFilterFields = (filters: Record<string, any[]>, matchModes: Record<string, string>) => {
    return Object.keys(filters).map(field => {
        const matchMode = matchModes[field] || "exact";
        const fieldFilters = filters[field] || [];
        return {
            field,
            filters: fieldFilters.map(value => ({ value, matchMode }))
        };
    });
};

/**
 * 全局搜索输入组件
 */
const GlobalSearchInput: React.FC<{ onChange: (e: React.ChangeEvent<HTMLInputElement>) => void }> = ({ onChange }) => (
    <Input
        placeholder="Search"
        allowClear
        onChange={onChange}
        style={{ width: 280, height: "32px", float: "right", borderRadius: "2px" }}
    />
);

// ==================== 类型定义 ====================

interface WirelessCustomTableProps {
    quantity?: number;
    columns: any[];
    rowSelection?: any;
    matchFieldsList?: any[];
    searchFieldsList?: any[];
    extraButton?: React.ReactNode;
    helpDraw?: React.ReactNode;
    fetchAPIInfo: (...args: any[]) => Promise<any>;
    fetchAPIParams?: any[];
    isShowPagination?: boolean;
    disableInternalRowSelection?: boolean;
    showColumnSelector?: boolean;
    columnsOrder?: boolean;
    resizableColumns?: boolean;
    tableId?: string;
    [key: string]: any;
}

interface WirelessCustomTableRef {
    refreshTable: () => void;
    setTableLoading: (value: boolean) => void;
    getSelectedRow: () => { tableSelectedRowKey: any[]; tableSelectedRows: any[] };
    getRemovedRow: () => { tableRemovedRowKey: any[]; tableRemovedRows: any[] };
    clearSelectedRow: () => void;
    getOperations: () => Record<string, string>;
    getOperationRowsMappings: () => Record<string, any>;
    getTableData: () => any[];
    clearAndRefresh: () => void;
    refreshAndSaveSelectedRow: () => void;
}

// ==================== 主组件 ====================

export const WirelessCustomTable = forwardRef<WirelessCustomTableRef, WirelessCustomTableProps>(
    (
        {
            quantity,
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            disableInternalRowSelection,
            showColumnSelector = false,
            columnsOrder = false,
            resizableColumns = false,
            tableId,
            ...props
        },
        ref
    ) => {
        // ==================== Preferences 相关状态和逻辑 ====================

        const { setPref } = useAuth();
        const [visibleColumns, setVisibleColumns] = useState<string[]>([]);
        const [isInitialized, setIsInitialized] = useState(false);

        // 判断是否需要启用 preferences 功能
        const shouldEnablePreferences = !!(tableId && (columnsOrder || showColumnSelector));

        const { data: preferences, isLoading: preferencesLoading } = useGetPreferences({
            enabled: shouldEnablePreferences,
        });

        // 稳定化 columns 引用
        const stableColumns = useMemo(() => columns, [
            JSON.stringify(columns?.map((col: any) => ({ key: col.key, dataIndex: col.dataIndex })))
        ]);

        // Preferences 工具函数
        const getPreference = useCallback((preference: string) => {
            if (!preferences) return null;
            for (const pref of preferences) {
                if (pref.tag === preference) return pref.value;
            }
            return null;
        }, [preferences]);

        // 获取默认可见列
        const getDefaultVisibleColumns = useCallback(() => {
            return stableColumns.map((col: any) => col.key || col.dataIndex);
        }, [stableColumns]);

        // 初始化列配置
        useEffect(() => {
            if (!shouldEnablePreferences) {
                // 不需要 preferences 功能时，直接使用默认值
                if (!isInitialized && stableColumns) {
                    setVisibleColumns(getDefaultVisibleColumns());
                    setIsInitialized(true);
                }
                return;
            }

            // 需要 preferences 功能时，等待数据加载完成
            if (!tableId || !stableColumns || isInitialized || preferencesLoading) return;

            try {
                const savedVisibleColumns = getPreference(`table_${tableId}_visible_columns`);
                const savedColumnOrder = getPreference(`table_${tableId}_column_order`);

                let newVisibleColumns: string[] = [];

                if (savedVisibleColumns) {
                    newVisibleColumns = JSON.parse(savedVisibleColumns);
                } else if (savedColumnOrder) {
                    newVisibleColumns = JSON.parse(savedColumnOrder);
                } else {
                    newVisibleColumns = getDefaultVisibleColumns();
                }

                // 如果有保存的列顺序，按照该顺序重新排列可见列
                if (savedColumnOrder) {
                    const columnOrder = JSON.parse(savedColumnOrder);
                    const orderedVisibleColumns: string[] = [];

                    // 按保存的顺序添加列
                    columnOrder.forEach((key: string) => {
                        if (newVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    // 添加不在顺序中但可见的列
                    newVisibleColumns.forEach(key => {
                        if (!orderedVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    newVisibleColumns = orderedVisibleColumns;
                }

                setVisibleColumns(newVisibleColumns);
            } catch (e) {
                // 出错时使用默认值
                setVisibleColumns(getDefaultVisibleColumns());
            }

            setIsInitialized(true);
        }, [
            tableId,
            stableColumns,
            preferences,
            preferencesLoading,
            isInitialized,
            shouldEnablePreferences,
            getPreference,
            getDefaultVisibleColumns
        ]);

        // ==================== 列配置处理逻辑 ====================

        // 根据可见列配置过滤和排序列（规范化 fixed 列在左右两侧的位置）
        const filteredColumns = useMemo(() => {
            if (!stableColumns) return [];

            // 规范化顺序方法：left-fixed -> normal -> right-fixed，并保持各自组内与 ColumnSelector 一致的顺序
            const normalizeOrder = (keys: string[]) => {
                const leftSet = new Set(
                    stableColumns
                        .filter((c: any) => c.fixed === 'left')
                        .map((c: any) => c.key || c.dataIndex)
                );
                const rightSet = new Set(
                    stableColumns
                        .filter((c: any) => c.fixed === 'right')
                        .map((c: any) => c.key || c.dataIndex)
                );

                const left: string[] = [];
                const middle: string[] = [];
                const right: string[] = [];

                keys.forEach((k) => {
                    if (leftSet.has(k)) left.push(k);
                    else if (rightSet.has(k)) right.push(k);
                    else middle.push(k);
                });

                return [...left, ...middle, ...right];
            };

            const rawVisibleKeys = visibleColumns.length > 0
                ? visibleColumns
                : stableColumns.map((col: any) => col.key || col.dataIndex);

            const visibleKeys = normalizeOrder(rawVisibleKeys);

            // 过滤出可见的列
            const visibleColumnsData = stableColumns.filter((col: any) => {
                const key = col.key || col.dataIndex;
                return visibleKeys.includes(key);
            });

            // 按规范化后的顺序排序
            return visibleColumnsData.sort((a: any, b: any) => {
                const keyA = a.key || a.dataIndex;
                const keyB = b.key || b.dataIndex;
                const indexA = visibleKeys.indexOf(keyA);
                const indexB = visibleKeys.indexOf(keyB);
                return indexA - indexB;
            });
        }, [stableColumns, visibleColumns]);

        // 保存列可见性配置
        const saveVisibleColumns = useCallback(async (newVisibleColumns: string[]) => {
            // 始终更新本地状态，保证在未传 tableId（不持久化）的情况下也能正常生效
            setVisibleColumns(newVisibleColumns);

            // 仅当提供了 tableId 时才持久化到偏好设置
            if (!tableId) return;

            try {
                await setPref({
                    preference: `table_${tableId}_visible_columns`,
                    value: JSON.stringify(newVisibleColumns)
                });
            } catch (e) {
                // console.error('Error saving visible columns:', e);
            }
        }, [tableId, setPref]);

        // 保存列顺序配置
        const saveColumnOrder = useCallback(async (newColumnOrder: string[]) => {
            // 未提供 tableId 时不做持久化，但也不应阻断功能
            if (!tableId) return;

            try {
                await setPref({
                    preference: `table_${tableId}_column_order`,
                    value: JSON.stringify(newColumnOrder)
                });
            } catch (e) {
                // console.error('Error saving column order:', e);
            }
        }, [tableId, setPref]);

        // 处理列可见性变化
        const handleVisibleColumnsChange = useCallback((newVisibleColumns: string[]) => {
            const fixedKeepVisibleKeys = stableColumns
                .filter((col: any) => col.columnsFix)
                .map((col: any) => col.key || col.dataIndex);

            // 合并并去重，确保 columnsFix 的列始终可见
            const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeepVisibleKeys]));

            // 规范化顺序：保证 fixed:'left' 在最左、fixed:'right' 在最右
            const leftSet = new Set(
                stableColumns
                    .filter((c: any) => c.fixed === 'left')
                    .map((c: any) => c.key || c.dataIndex)
            );
            const rightSet = new Set(
                stableColumns
                    .filter((c: any) => c.fixed === 'right')
                    .map((c: any) => c.key || c.dataIndex)
            );

            const left: string[] = [];
            const middle: string[] = [];
            const right: string[] = [];
            merged.forEach((k) => {
                if (leftSet.has(k)) left.push(k);
                else if (rightSet.has(k)) right.push(k);
                else middle.push(k);
            });
            const normalized = [...left, ...middle, ...right];

            // 保存可见列配置（顺序已规范化）
            saveVisibleColumns(normalized);
        }, [stableColumns, saveVisibleColumns]);

        // 处理列顺序变化
        const handleColumnOrderChange = useCallback((newColumnOrder: string[]) => {
            // 找出所有fixed: 'left'和fixed: 'right'的列
            const leftFixedColumns = stableColumns
                .filter((col: any) => col.fixed === 'left')
                .map((col: any) => col.key || col.dataIndex);

            const rightFixedColumns = stableColumns
                .filter((col: any) => col.fixed === 'right')
                .map((col: any) => col.key || col.dataIndex);

            // 从newColumnOrder中移除所有fixed列
            let filteredOrder = newColumnOrder.filter(key =>
                !leftFixedColumns.includes(key) && !rightFixedColumns.includes(key)
            );

            // 将fixed: 'left'的列放在最前面，fixed: 'right'的列放在最后面
            const finalOrder = [...leftFixedColumns, ...filteredOrder, ...rightFixedColumns];

            // 保存最终的列顺序
            saveColumnOrder(finalOrder);
        }, [saveColumnOrder, stableColumns]);

        // 处理可调整大小的列
        const processedColumns = useMemo(() => {
            if (!resizableColumns) {
                return filteredColumns;
            }

            return filteredColumns.map((column: any) => {
                const identity = column.key || column.dataIndex;
                // 查找原始列配置，确保保留所有属性
                const originalColumn = stableColumns.find((col: any) =>
                    (col.key || col.dataIndex) === identity
                ) || column;

                // 确保resizable属性被正确保留，即使列被取消勾选再勾选
                const isResizable = originalColumn.resizable !== false;

                // 创建一个新的列对象，确保保留所有原始属性
                const processedColumn: any = {
                    // 以原始列为基准，保留原有属性
                    ...originalColumn,
                    // 可见列上的变更覆盖
                    ...column,
                };

                // 补充稳定的 key，避免只存在 dataIndex 导致匹配异常
                processedColumn.key = originalColumn.key || originalColumn.dataIndex || identity;

                // 明确设置resizable属性和基础尺寸，防止被丢失
                if (isResizable) {
                    processedColumn.resizable = true;
                    // 不要默认将 minWidth 或 width 设为 1，保留未指定状态以让 AntD 自适应列宽
                    processedColumn.minWidth = (originalColumn.minWidth !== undefined) ? originalColumn.minWidth : processedColumn.minWidth;
                    processedColumn.width = (processedColumn.width !== undefined) ? processedColumn.width : originalColumn.width;
                } else {
                    processedColumn.resizable = false;
                }

                return processedColumn;
            });
        }, [filteredColumns, resizableColumns, stableColumns]);

        // 构建最终的列配置（包含列选择器）
        const finalColumns = useMemo(() => {
            if (!showColumnSelector) {
                return processedColumns;
            }

            return [
                ...processedColumns,
                {
                    title: (
                        <div>
                            <ColumnSelector
                                columns={columns}
                                visibleColumns={visibleColumns}
                                onChange={handleVisibleColumnsChange}
                                draggable={columnsOrder}
                                onDragEnd={handleColumnOrderChange}
                            />
                        </div>
                    ),
                    key: 'columnSelector',
                    width: 44,
                    fixed: 'right' as const,
                    resizable: false,
                    onHeaderCell: () => ({
                        style: {
                            width: '100%',
                            padding: 0,
                            height: '54px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }
                    }),
                }
            ];
        }, [
            processedColumns,
            showColumnSelector,
            columns,
            visibleColumns,
            handleVisibleColumnsChange,
            columnsOrder,
            handleColumnOrderChange
        ]);
        // console.log(finalColumns)

        // ==================== 表格状态管理 ====================

        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);
        const dispatch = useDispatch();

        // 行选择相关状态
        const [tableSelectedRowKey, setTableSelectedRowKey] = useState<any[]>(
            rowSelection ? rowSelection.selectedRowKeys || [] : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState<any[]>(
            rowSelection ? rowSelection.selectedRows || [] : []
        );
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState<any[]>([]);
        const [tableRemovedRows, setTableRemovedRows] = useState<any[]>([]);
        const [operations, setOperations] = useState<Record<string, string>>({});
        const [operationRowsMappings, setOperationRowsMappings] = useState<Record<string, any>>({});

        // 排序和过滤状态
        const [sorter, setSorter] = useState<any>({});
        const [filters, setFilters] = useState<Record<string, any>>({});

        // 检查默认排序列
        const checkSortedColumn = useCallback((columns: any[]) => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    if (columnConfig.defaultSortOrder !== null) {
                        return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                    }
                }
            }
            return [undefined, undefined];
        }, []);

        // ==================== 行选择处理逻辑 ====================

        // 处理单行选择
        const handleSelect = useCallback((record: any, selected: boolean) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter((item: any) => item !== record.id);

            // 更新移除的行记录
            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter((item: any) => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter((item: any) => item.id !== record.id);

            // 处理操作状态
            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = { ...operations };
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            // 处理父子关系 - 子项选择时移除父项
            rows.forEach((row: any) => {
                if (row.children) {
                    const isInChildren = row.children.some((child: any) => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex((r: any) => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex((k: any) => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            // 处理父子关系 - 父项选择时处理子项
            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach((child: any) => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some((row: any) => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map((child: any) => child.id);
                    needRemoveRows.forEach((id: any) => {
                        const rowIndex = rows.findIndex((r: any) => r.id === id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }
                        const keyIndex = keys.findIndex((k: any) => k === id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    });
                }
            }

            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        }, [
            tableSelectedRowKey,
            tableRemovedRowKey,
            tableRemovedRows,
            tableSelectedRows,
            operations,
            rowSelection
        ]);

        // 处理全选/取消全选
        const handleSelectAll = useCallback((selected: boolean, selectedRows: any[], changeRows: any[]) => {
            if (quantity) {
                // 有数量限制的情况
                const currentCount = tableSelectedRowKey.length;
                const remaining = quantity - currentCount;

                if (selected && remaining <= 0) {
                    return; // 已满，不允许再选
                }

                const limitedRows = selected ? changeRows.slice(0, remaining) : changeRows;
                const limitedIds = limitedRows.map((item: any) => item.id);

                const keys = selected
                    ? Array.from(new Set([...tableSelectedRowKey, ...limitedIds]))
                    : tableSelectedRowKey.filter((item: any) => !limitedIds.includes(item));
                setTableSelectedRowKey(keys);

                const rows = selected
                    ? Array.from(new Set([...tableSelectedRows, ...limitedRows]))
                    : tableSelectedRows.filter((item: any) => !limitedIds.includes(item.id));
                setTableSelectedRows(rows);

                // 更新移除记录
                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...limitedIds]);
                    setTableRemovedRows([...tableRemovedRows, ...limitedRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => !limitedIds.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter((item: any) => !limitedIds.includes(item.id)));
                }

                // 处理操作状态
                if (changeRows.length > 0 && Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    limitedRows.forEach((record: any) => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            } else {
                // 无数量限制的情况
                const ids = changeRows.map((item: any) => item.id);
                const keys = selected
                    ? tableSelectedRowKey.concat(ids)
                    : tableSelectedRowKey.filter((item: any) => !ids.includes(item));
                setTableSelectedRowKey(keys);

                // 更新移除记录
                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                    setTableRemovedRows([...tableRemovedRows, ...changeRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => !ids.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter((item: any) => !ids.includes(item.id)));
                }

                const rows = selected
                    ? [...tableSelectedRows, ...changeRows]
                    : tableSelectedRows.filter((item: any) => !ids.includes(item.id));
                setTableSelectedRows(rows);

                // 处理默认选中行的操作状态
                if (changeRows.length > 0 && Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    changeRows.forEach((record: any) => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }

                        setOperationRowsMappings(prev => ({
                            ...prev,
                            [record.id]: record
                        }));
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            }
        }, [
            quantity,
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey,
            tableRemovedRows,
            operations,
            rowSelection
        ]);

        // 构建表格行选择配置
        const tableRowSelection = useMemo(() => {
            if (rowSelection?.type === "radio") {
                return rowSelection;
            }

            if (disableInternalRowSelection && rowSelection) {
                return rowSelection;
            }

            return {
                selectedRowKeys: tableSelectedRowKey,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
                getCheckboxProps: rowSelection?.getCheckboxProps,
                fixed: rowSelection?.fixed,
                checkStrictly: rowSelection?.checkStrictly
            };
        }, [
            disableInternalRowSelection,
            rowSelection?.type,
            rowSelection?.getCheckboxProps,
            rowSelection?.fixed,
            rowSelection?.checkStrictly,
            tableSelectedRowKey,
            handleSelect,
            handleSelectAll
        ]);

        // ==================== 组件引用方法 ====================

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData();
            },
            setTableLoading(value: boolean) {
                setLoading(value);
            },
            getSelectedRow: () => ({
                tableSelectedRowKey,
                tableSelectedRows
            }),
            getRemovedRow: () => ({
                tableRemovedRowKey,
                tableRemovedRows
            }),
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => operations,
            getOperationRowsMappings: () => operationRowsMappings,
            getTableData: () => data,
            clearAndRefresh: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
                fetchData(true);
            },
            refreshAndSaveSelectedRow: () => {
                fetchData(true);
            }
        }), [
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey,
            tableRemovedRows,
            operations,
            operationRowsMappings,
            data,
            setLoading,
            setTableSelectedRowKey,
            setTableSelectedRows
        ]);

        // ==================== 数据获取逻辑 ====================

        const fetchData = useCallback(async (ignoreSelection = false) => {
            setLoading(true);

            try {
                // 构建过滤和排序参数
                const filterFields = filters ? createFilterFields(filters, matchModes) : [];
                const sortFields: any[] = [];

                if (sorter.field && sorter.order) {
                    sortFields.push({
                        field: sorter.field,
                        order: sorter.order === "ascend" ? "asc" : "desc"
                    });
                }

                // 调用API获取数据
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                            ...fetchAPIParams,
                            pagination.current,
                            pagination.pageSize,
                            filterFields,
                            sortFields,
                            searchFields
                        ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );

                // 如果当前页没有数据但总数不为0，跳转到最后一页
                if (response.data.length === 0 && response.total !== 0) {
                    const lastPage = Math.ceil(response.total / response.pageSize);
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [...fetchAPIParams, lastPage, pagination.pageSize, [], [], searchFields]
                            : [lastPage, pagination.pageSize, [], [], searchFields])
                    );
                }

                const responseDataTemp = JSON.parse(JSON.stringify(response.data));

                // 处理选择状态
                if (!ignoreSelection) {
                    responseDataTemp.forEach((item: any) => {
                        item.selected = operations[item.id] === "add" ? true : item.selected;
                    });

                    if (responseDataTemp.every((item: any) => "selected" in item)) {
                        const backendSelectedRowKeys = responseDataTemp
                            .filter((item: any) => item.selected)
                            .map((item: any) => item.id);

                        const frontendSelectedRowKeys = tableSelectedRowKey
                            ? responseDataTemp
                                .filter((item: any) => tableSelectedRowKey.indexOf(item.id) > -1)
                                .map((item: any) => item.id)
                            : [];

                        const removedRowKeys = tableRemovedRowKey
                            ? responseDataTemp
                                .filter((item: any) => tableRemovedRowKey.indexOf(item.id) > -1)
                                .map((item: any) => item.id)
                            : [];

                        const selectedRowKeys = Array.from(
                            new Set([
                                ...(tableSelectedRowKey || []),
                                ...backendSelectedRowKeys,
                                ...frontendSelectedRowKeys
                            ])
                        ).filter((itemId: any) => removedRowKeys.indexOf(itemId) === -1);

                        setTableSelectedRowKey(selectedRowKeys);
                        setTableSelectedRows(responseDataTemp.filter((item: any) => item.selected));
                    }
                }

                setData(responseDataTemp);
                setPagination((prev: any) => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                // console.error("Error fetching data:", error);
            } finally {
                setLoading(false);
            }
        }, [
            filters,
            matchModes,
            sorter,
            fetchAPIInfo,
            fetchAPIParams,
            pagination.current,
            pagination.pageSize,
            searchFields,
            operations,
            tableSelectedRowKey,
            tableRemovedRowKey
        ]);

        // ==================== 副作用和事件处理 ====================

        // 初始化默认排序
        useEffect(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(columns);
            if (sortedColumn) {
                setSorter({
                    field: sortedColumn,
                    order: sortedOrder
                });
            }
        }, [columns, checkSortedColumn]);

        // 数据获取副作用
        useEffect(() => {
            fetchData();
        }, [fetchData]);

        // 搜索变化处理
        const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));

            // 清空选择状态
            if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                tableRowSelection.selectedRowKeys = [];
            }
            setTableSelectedRows([]);
            setTableSelectedRowKey([]);

            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        }, [dispatch, tableRowSelection, searchFieldsList, setSearchFields]);

        // 表格变化处理
        const tableChange = useCallback(async (pagination: any, filters: any, sorter: any) => {
            // 添加延迟以确保状态更新
            await new Promise(resolve => setTimeout(resolve, 100));

            setSorter(sorter);
            setFilters(filters);

            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        }, [
            searchFields,
            fetchAPIInfo,
            fetchAPIParams,
            matchModes,
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey
        ]);



        // ==================== 组件渲染 ====================

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" }}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>

                    <div className={styles.wirelessCustomTable}>
                        {(() => {
                            const TableComponent = resizableColumns ? ResizableTable : Table;

                            const tableProps = {
                                rowSelection: rowSelection ? tableRowSelection : null,
                                columns: finalColumns,
                                bordered: true,
                                rowKey: (record: any) => record.id,
                                loading,
                                dataSource: data,
                                pagination: searchFieldsList || isShowPagination ? pagination : false,
                                onChange: tableChange,
                                scroll: { x: 'max-content' },
                                ...(resizableColumns ? { resizableColumns: true } : {}),
                                ...props
                            };

                            return <TableComponent {...tableProps} />;
                        })()}
                    </div>
                </Flex>
            </div>
        );
    }
);
