/* 隐藏表格最右边的竖线 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td > .ant-table-expanded-row-fixed::after) {
    display: none !important;
}

/* 移除表格容器的右边框 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container) {
    border-right: none !important;
}

/* 移除表格最后一列的右边框 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td:last-child) {
    border-right: none !important;
}
