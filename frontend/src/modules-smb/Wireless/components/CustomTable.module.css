/* 修复缩放时表格右边多出的竖线问题 */
.wirelessCustomTable {
    /* 确保容器不会产生额外的边框 */
    position: relative;
    overflow: hidden;
}

/* 隐藏所有可能产生右边竖线的伪元素 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > th > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td > .ant-table-expanded-row-fixed::after),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td > .ant-table-expanded-row-fixed::after) {
    display: none !important;
}

/* 移除表格容器的右边框 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container) {
    border-right: none !important;
    /* 防止缩放时出现额外边框 */
    box-sizing: border-box;
}

/* 移除表格最后一列的右边框 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > th:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td:last-child),
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td:last-child) {
    border-right: none !important;
}

/* 修复缩放时可能出现的额外边框 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered) {
    /* 确保表格本身不会产生额外的右边框 */
    border-right: none !important;
}

/* 针对固定列的特殊处理 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table-container .ant-table-content table colgroup col:last-child) {
    /* 防止最后一列产生额外宽度 */
    width: auto !important;
}

/* 处理滚动条可能导致的边框问题 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table-container .ant-table-body) {
    /* 确保表格主体不会产生右边框 */
    border-right: none !important;
}

/* 处理表头可能的边框问题 */
.wirelessCustomTable :global(.ant-table-wrapper .ant-table-container .ant-table-header) {
    border-right: none !important;
}

/* 在不同缩放级别下确保样式一致性 */
@media screen and (min-resolution: 1dppx) {
    .wirelessCustomTable :global(.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container) {
        border-right: none !important;
        /* 强制移除可能的子像素渲染边框 */
        transform: translateZ(0);
    }
}